from polyfactory.factories.pydantic_factory import ModelFactory
from polyfactory.factories.sqlalchemy_factory import SQLAlchemyFactory

from holmatro_customer_portal.database.models import (
    Attribute,
    AttributeTranslation,
    Category,
    CategoryTranslation,
    DBFilter,
    Language,
    Product,
    User,
)
from holmatro_customer_portal.schemas.syncforce_product_schema import Category as SFCategory


class FilterFactory(SQLAlchemyFactory[DBFilter]): ...


class AttributeFactory(SQLAlchemyFactory[Attribute]): ...


class AttributeTranslationFactory(SQLAlchemyFactory[AttributeTranslation]): ...


class ProductFactory(SQLAlchemyFactory[Product]): ...


class SFCategoryFactory(ModelFactory[SFCategory]): ...


class LanguageFactory(SQLAlchemyFactory[Language]): ...


class CategoryFactory(SQLAlchemyFactory[Category]):
    deleted_at = None


class CategoryTranslationFactory(SQLAlchemyFactory[CategoryTranslation]): ...


class UserFactory(SQLAlchemyFactory[User]): ...
