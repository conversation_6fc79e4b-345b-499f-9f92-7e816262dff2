import uuid
from collections import namedtuple
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock
from sqlalchemy.engine import Row

from holmatro_customer_portal.data.holmatro_catalog_repository import HolmatroCatalogRepository
from holmatro_customer_portal.database.models import (
    CategoryAssociation,
    ProductNameTranslation,
    category_user_association_table,
)
from holmatro_customer_portal.schemas.opensearch_schemas import (
    AutocompleteProduct,
    AutocompleteResponse,
    TranslatedName,
)
from holmatro_customer_portal.schemas.response_schema import AutocompleteRes, ProductAutocomplete, SearchParamsReq
from holmatro_customer_portal.services.get_autocomplete import get_autocomplete_handle
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import Autocomplete, AutocompleteSuggestion, Product
from tests.factories import CategoryFactory, LanguageFactory, ProductFactory, UserFactory
from tests.fixtures.database_models_fixture import mocked_user


def _setup_common_test_data(session, accessible_category_id="7411", restricted_category_id=None, products_data=None):
    language = LanguageFactory.build(language_code="en")
    session.add(language)
    session.flush()

    user = UserFactory.build(main_language_id=language.id, main_language=language)
    session.add(user)
    session.flush()

    accessible_category = CategoryFactory.build(category_id=accessible_category_id)
    session.add(accessible_category)
    session.flush()

    session.execute(
        category_user_association_table.insert(), {"user_id": user.id, "category_id": accessible_category.id}
    )

    restricted_category = None
    if restricted_category_id:
        restricted_category = CategoryFactory.build(category_id=restricted_category_id)
        session.add(restricted_category)
        session.flush()

    created_products = []
    if products_data:
        for p_data in products_data:
            product = ProductFactory.build(product_id=p_data["product_id"], article_number=p_data["article_number"])
            session.add(product)
            session.flush()
            created_products.append(product)

            product_name = ProductNameTranslation(product_id=product.id, language_id=language.id, value=p_data["name"])
            session.add(product_name)

            category_assoc = CategoryAssociation(
                product_id=product.id,
                category_id=accessible_category.id if p_data.get("accessible", True) else restricted_category.id,
            )
            session.add(category_assoc)
        session.commit()

    return language, user, accessible_category, restricted_category, created_products


class TestGetAutocomplete:
    product_id = uuid.uuid4()
    language_id = uuid.uuid4()
    query_result_model = namedtuple("query_result", "product_id product_name category_id")

    @pytest.mark.parametrize(
        "tweakwise_response, query_result, expected_result",
        [
            (
                Autocomplete(
                    items=[],
                    suggestions=[
                        AutocompleteSuggestion(title="chain"),
                        AutocompleteSuggestion(title="cylinder"),
                    ],
                ),
                None,
                AutocompleteRes(
                    products=[],
                    suggestions=["chain", "cylinder"],
                ),
            ),
            (
                Autocomplete(
                    items=[],
                    suggestions=[],
                ),
                None,
                AutocompleteRes(products=[], suggestions=[]),
            ),
            (
                Autocomplete(
                    items=[
                        Product(
                            item_no="1",
                            title="product 1",
                            brand="Holmatro",
                        ),
                    ],
                    suggestions=[AutocompleteSuggestion(title="chain")],
                ),
                [],
                AutocompleteRes(
                    products=[],
                    suggestions=["chain"],
                ),
            ),
            (
                Autocomplete(
                    items=[
                        Product(
                            item_no="1",
                            title="product 1",
                            brand="Holmatro",
                        ),
                        Product(
                            item_no="1-1-2",
                            title="product 2",
                            brand="Holmatro",
                        ),
                    ],
                    suggestions=[AutocompleteSuggestion(title="chain")],
                ),
                [
                    query_result_model(product_id, "product 1", 1),
                    query_result_model(product_id, "product 2", 2),
                ],
                AutocompleteRes(
                    products=[
                        ProductAutocomplete(product_id=product_id, name="product 1", category_id=1),
                        ProductAutocomplete(product_id=product_id, name="product 2", category_id=2),
                    ],
                    suggestions=["chain"],
                ),
            ),
        ],
    )
    @patch("holmatro_customer_portal.services.get_autocomplete.get_autocomplete_products")
    def test_get_autocomplete(
        self,
        mock_get_products_from_db: MagicMock,
        tweakwise_response: Autocomplete,
        query_result: list[Row],
        expected_result: AutocompleteRes,
        catalog_repo_mock: MagicMock,
    ):
        session = UnifiedAlchemyMagicMock()
        mock_get_products_from_db.return_value = query_result
        catalog_repo_mock.get_autocomplete.return_value = tweakwise_response
        params = SearchParamsReq(search_query="c", category_path=None)

        res = get_autocomplete_handle(catalog_repo_mock, params, mocked_user, session)

        assert res == expected_result

        catalog_repo_mock.get_autocomplete.assert_called_once_with(params, "nl")

    def test_get_autocomplete_handle_integration_with_real_db(self, session):
        """Integration test for get_autocomplete_handle using real database session."""
        products_data = [
            {"product_id": 12345, "article_number": "HYD-001", "name": "Database Hydraulic Pump"},
            {"product_id": 12346, "article_number": "HYD-002", "name": "Database Hydraulic Tool"},
        ]
        language, user, category, _, created_products = _setup_common_test_data(session, products_data=products_data)

        product1, product2 = created_products

        # Create mock repository with real database
        mock_opensearch_client = Mock()
        mock_opensearch_response = AutocompleteResponse(
            suggestions=["hydraulic", "hydraulic pump"],
            products=[
                AutocompleteProduct(
                    id=uuid.uuid4(),
                    product_id=product1.product_id,
                    product_names=[TranslatedName(language_code="en", value="Hydraulic Pump")],
                    score=0.9,
                ),
                AutocompleteProduct(
                    id=uuid.uuid4(),
                    product_id=product2.product_id,
                    product_names=[TranslatedName(language_code="en", value="Hydraulic Tool")],
                    score=0.8,
                ),
            ],
        )
        mock_opensearch_client.autocomplete.return_value = mock_opensearch_response

        repository = HolmatroCatalogRepository(db=session, opensearch_client=mock_opensearch_client)
        params = SearchParamsReq(search_query="hydraulic")

        # Act
        result = get_autocomplete_handle(repository, params, user, session)

        # Assert
        assert isinstance(result, AutocompleteRes)
        assert len(result.suggestions) == 2
        assert result.suggestions == ["hydraulic", "hydraulic pump"]
        assert len(result.products) == 2

        # Check that products were found in database
        assert result.products[0].name == "Database Hydraulic Pump"
        assert result.products[1].name == "Database Hydraulic Tool"
        assert result.products[0].category_id == int(category.category_id)
        assert result.products[1].category_id == int(category.category_id)

    def test_get_autocomplete_handle_no_database_matches(self, session):
        """Test get_autocomplete_handle when OpenSearch returns products not in database."""
        _, user, _, _, _ = _setup_common_test_data(session)

        # Create mock repository
        mock_opensearch_client = Mock()
        mock_opensearch_response = AutocompleteResponse(
            suggestions=["nonexistent"],
            products=[
                AutocompleteProduct(
                    id=uuid.uuid4(),
                    product_id=99999,  # Product ID that doesn't exist in database
                    product_names=[TranslatedName(language_code="en", value="Nonexistent Product")],
                    score=0.9,
                )
            ],
        )
        mock_opensearch_client.autocomplete.return_value = mock_opensearch_response

        repository = HolmatroCatalogRepository(db=session, opensearch_client=mock_opensearch_client)
        params = SearchParamsReq(search_query="nonexistent")

        # Act
        result = get_autocomplete_handle(repository, params, user, session)

        # Assert - should return suggestions but no products since none were found in database
        assert isinstance(result, AutocompleteRes)
        assert len(result.suggestions) == 1
        assert result.suggestions == ["nonexistent"]
        assert len(result.products) == 0

    def test_get_autocomplete_handle_empty_opensearch_response(self, session):
        """Test get_autocomplete_handle with empty OpenSearch response."""
        _, user, _, _, _ = _setup_common_test_data(session)

        # Create mock client with empty response
        mock_opensearch_client = Mock()
        mock_opensearch_response = AutocompleteResponse(suggestions=[], products=[])
        mock_opensearch_client.autocomplete.return_value = mock_opensearch_response

        repository = HolmatroCatalogRepository(db=session, opensearch_client=mock_opensearch_client)
        params = SearchParamsReq(search_query="empty")

        # Act
        result = get_autocomplete_handle(repository, params, user, session)

        # Assert
        assert isinstance(result, AutocompleteRes)
        assert len(result.suggestions) == 0
        assert len(result.products) == 0

    def test_get_autocomplete_handle_user_category_restrictions(self, session):
        """Test get_autocomplete_handle respects user category access restrictions."""
        products_data = [
            {"product_id": 12345, "article_number": "ACC-001", "name": "Accessible Product", "accessible": True},
            {"product_id": 12346, "article_number": "RES-001", "name": "Restricted Product", "accessible": False},
        ]
        language, user, accessible_category, restricted_category, created_products = _setup_common_test_data(
            session, restricted_category_id="2838", products_data=products_data
        )

        # Create mock response with both products
        mock_opensearch_client = Mock()
        mock_opensearch_response = AutocompleteResponse(
            suggestions=["product"],
            products=[
                AutocompleteProduct(
                    id=uuid.uuid4(),
                    product_id=12345,
                    product_names=[TranslatedName(language_code="en", value="Accessible Product")],
                    score=0.9,
                ),
                AutocompleteProduct(
                    id=uuid.uuid4(),
                    product_id=12346,
                    product_names=[TranslatedName(language_code="en", value="Restricted Product")],
                    score=0.8,
                ),
            ],
        )
        mock_opensearch_client.autocomplete.return_value = mock_opensearch_response

        repository = HolmatroCatalogRepository(db=session, opensearch_client=mock_opensearch_client)
        params = SearchParamsReq(search_query="product")

        # Act
        result = get_autocomplete_handle(repository, params, user, session)

        # Assert - should only return accessible product
        assert isinstance(result, AutocompleteRes)
        assert len(result.products) == 1
        assert result.products[0].name == "Accessible Product"
        assert result.products[0].category_id == int(accessible_category.category_id)
