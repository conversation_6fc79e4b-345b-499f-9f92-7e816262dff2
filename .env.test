export DATABASE_NAME="holmatro_cp_db"
export DATABASE_PASSWORD="Holmatro_pw"
export DATABASE_USER="root"
export DATABASE_PORT=3306
export DATABASE_HOST="localhost"
export DATABASE_WRITE_HOST="localhost"
export DATABASE_READ_HOST="localhost"
export MYSQL_ARCH=mysql:8

export UVICORN_PORT=80
export UVICORN_SERVER_HOST="0.0.0.0"

export TWEAKWISE_INSTANCE_KEY=
export TWEAKWISE_TOKEN=

export HOLMATRO_ASSETS_BUCKET_NAME="HOLMATRO_ASSETS_BUCKET_NAME"

export AWS_ACCESS_KEY_ID='testKeysID'
export AWS_SECRET_ACCESS_KEY='testKeys'
export AWS_SESSION_TOKEN="+/7lCMj36w4F1snER3RkDQeNZJe"
export AWS_DEFAULT_REGION="us-east-1"

export B2C_TENANT_ID="32fc4a30-"

export MY_HM_BASE_URL="http://example.com"
export MY_HM_SUBSCRIPTION_TOKEN=066711
export JWT_ENCODING_KEY="my-key"

export SSM_HOLMATRO_PORTAL_API_KEY_NAME='secret_value'

export AZURE_API_URL="localhost:8000"
export AZURE_API_KEY="test"

export RABBITMQ_BROKER_URL="test_url"

export SYNC_FORCE_PROPOSITION_ID="1"

export OPENSEARCH_HOST=localhost
export OPENSEARCH_PORT=9200
export OPENSEARCH_USERNAME=""
export OPENSEARCH_PASSWORD=""
export OPENSEARCH_USE_SSL="false"
export OPENSEARCH_INDEX_NAME=products

export IS_HOLMATRO_CATALOG=true

export SERVICE_NAME="holmatro-customer-portal-dev"
export ENABLE_OTEL=false
