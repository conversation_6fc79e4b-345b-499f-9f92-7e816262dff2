from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.schemas.auth import UserLanguage
from holmatro_customer_portal.schemas.response_schema import ParamsReq, SearchFiltersReq, SearchParamsReq
from holmatro_customer_portal.services.utils.parse_params_and_filters import parse_params_and_filters
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.tweakwise.tweakwise_client import TweakwiseClient
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import (
    Autocomplete,
    Category,
    FacetsAttributes,
    FilterAttribute,
    Navigation,
    ProductsResponse,
)


class TweakwiseCatalogRepository(CatalogRepository):

    def __init__(self, tweakwise_client: TweakwiseClient):
        self._tweakwise_client = tweakwise_client

    def get_products(self, params: ParamsReq, filters: SearchFiltersReq | None = None) -> ProductsResponse:
        parsed_params = parse_params_and_filters(params, filters, default_tweakwise_params=True)
        return self._tweakwise_client.get_products(parsed_params)

    def get_category_tree(self, category_id: int) -> Category:
        return self._tweakwise_client.get_category_tree(category_id)

    def get_navigation(self, user: User, params: ParamsReq, filters: SearchFiltersReq | None = None) -> Navigation:
        parsed_params = parse_params_and_filters(params, filters, True)
        return self._tweakwise_client.get_navigation(parsed_params)

    def get_autocomplete(self, params: SearchParamsReq, language_code: str) -> Autocomplete:
        parsed_params = parse_params_and_filters(params, None, default_tweakwise_params=False)
        return self._tweakwise_client.get_autocomplete(parsed_params)

    def get_category_path(
        self, language: str, category: SyncforceCategory, parent_category: SyncforceCategory
    ) -> str | None:
        return self._tweakwise_client.get_category_path(language, category, parent_category)

    def get_facet_attributes(
        self, urlkey: str, category: SyncforceCategory, parent_category: SyncforceCategory
    ) -> list[FacetsAttributes | FilterAttribute] | None:
        return self._tweakwise_client.get_facet_attributes(urlkey, category, parent_category)  # type: ignore[return-value]  # FacetsAttributes is compatible with FacetsAttributes | FilterAttribute
