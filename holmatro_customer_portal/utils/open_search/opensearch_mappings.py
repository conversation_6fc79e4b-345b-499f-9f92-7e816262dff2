PRODUCT_INDEX_MAPPINGS = {
    "properties": {
        "id": {"type": "keyword"},
        "umid": {"type": "keyword"},
        "article_number": {"type": "keyword"},
        "master_code": {"type": "keyword"},
        "synonyms": {"type": "text"},
        "product_names": {
            "type": "nested",
            "properties": {
                "language_code": {"type": "keyword"},
                "value": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
            },
        },
        "master_product_names": {
            "type": "nested",
            "properties": {
                "language_code": {"type": "keyword"},
                "value": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
            },
        },
        "category_names": {
            "type": "nested",
            "properties": {
                "language_code": {"type": "keyword"},
                "value": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
            },
        },
        "category_ids": {"type": "keyword"},
        "filterable_attributes": {
            "type": "nested",
            "properties": {
                "attribute_id": {"type": "integer"},
                "attribute_name": {"type": "keyword"},
                "value_string": {"type": "keyword"},
                "value_numeric": {"type": "double"},
            },
        },
        # Completion suggester fields
        "product_name_suggest": {
            "type": "completion",
            "analyzer": "simple",
            "contexts": [{"name": "language_context", "type": "category"}],
            "preserve_separators": True,
            "preserve_position_increments": True,
            "max_input_length": 50,
        },
        "article_number_suggest": {
            "type": "completion",
            "analyzer": "keyword",
            "preserve_separators": True,
            "preserve_position_increments": True,
            "max_input_length": 50,
        },
    }
}
