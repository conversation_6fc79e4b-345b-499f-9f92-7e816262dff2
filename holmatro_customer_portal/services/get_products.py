from holmatro_customer_portal.data.tweakwise_repository import TweakwiseCatalogRepository
from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.response_schema import ParamsReq, ProductsWithFiltersRes, SearchFiltersReq
from holmatro_customer_portal.services.products.parse_products_and_properties import (
    get_db_products,
    parse_to_paginated_results,
)
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.enums import SelectionType
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import Facets, Filter, ResponseProperties


def get_products_with_filters_handle(
    catalog_repository: CatalogRepository,
    params: ParamsReq,
    filters: SearchFiltersReq | None,
    user: User,
    db: Session,
    my_hm_client: MyHolmatroClientInterface,
) -> ProductsWithFiltersRes:

    navigation_result = catalog_repository.get_navigation(user, params, filters)
    mapper = ProductPreviewMapper(db, my_hm_client)
    base_category_id = params.category_id
    sub_category_id = None

    if params.category_path:
        category_path_chunks = params.category_path.split("-")
        sub_category_id = int(category_path_chunks[-1][4:])

    db_products = (
        get_db_products(navigation_result, user, db, base_category_id, sub_category_id)
        if navigation_result.items
        else []
    )

    # This piece of code is for backwards compatibility with the old Tweakwise API.
    # The new OpenSearch API handles pagination of the products itself.
    # TODO: Update when support for Tweakwise API is removed.
    if catalog_repository is TweakwiseCatalogRepository:
        parsed_products, parsed_properties = parse_to_paginated_results(db_products, params)
    else:
        parsed_products = db_products
        nav_properties = navigation_result.properties
        parsed_properties = ResponseProperties(
            no_of_items=nav_properties.no_of_items,
            page_size=nav_properties.page_size,
            no_of_pages=nav_properties.no_of_pages,
            current_page=nav_properties.current_page,
            category_id=params.category_id,
        )

    products = mapper.product_preview_mapper(user, parsed_products)
    filtered_filters = _filter_out_unwanted_filters_and_attributes(navigation_result.facets)

    return ProductsWithFiltersRes(products=products, properties=parsed_properties, filters=filtered_filters)


def _filter_out_unwanted_filters_and_attributes(filters: list[Facets | Filter]) -> list[Facets | Filter]:
    filtered_filters = []
    for filter_item in filters:
        if filter_item.facet_settings.selection_type == SelectionType.SLIDER.value:
            # Filter out slider filters where min and max values are equal
            # Use the non-selected attributes for this check as the selected attributes represent the user selection
            min_max_attributes = [attr for attr in filter_item.attributes if not attr.is_selected]
            if len(min_max_attributes) == 1 or (
                len(min_max_attributes) == 2
                and float(min_max_attributes[0].title) != float(min_max_attributes[1].title)
            ):
                filtered_filters.append(filter_item)
        elif filter_item.facet_settings.selection_type == SelectionType.CHECKBOX.value:
            # Filter out checkbox filters with only a single attribute
            if len(filter_item.attributes) > 1:
                filtered_filters.append(filter_item)
    return filtered_filters
