from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.schemas.response_schema import AutocompleteRes, ProductAutocomplete, SearchParamsReq
from holmatro_customer_portal.services.utils.product_queries import get_autocomplete_products
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


def get_autocomplete_handle(
    catalog_repository: CatalogRepository, params: SearchParamsReq, user: User, db: Session
) -> AutocompleteRes:

    autocomplete_result = catalog_repository.get_autocomplete(params, user.language.code)

    autocomplete_suggestions = [suggestion.title for suggestion in autocomplete_result.suggestions]

    if not autocomplete_result.items:
        return AutocompleteRes(products=[], suggestions=autocomplete_suggestions)

    product_item_numbers = [int(product.item_no.split("-")[-1]) for product in autocomplete_result.items]

    # Remove any duplicate product ids
    product_item_numbers = list(dict.fromkeys(product_item_numbers))

    query_result = get_autocomplete_products(product_item_numbers, user, db)

    # If no products were found, just return an empty product listing. This could occur when none of the
    # products ids provided by Tweakwise are coupled to either the cutting or listing category, or when
    # none of the products are in the category that the user has access to (e.g. Tweakwise found lifting
    # products while user only has access to cutting).
    if not query_result:
        return AutocompleteRes(products=[], suggestions=autocomplete_suggestions)

    products = [
        ProductAutocomplete(
            product_id=product.product_id,
            name=product.product_name,
            category_id=product.category_id,
        )
        for product in query_result
    ]

    return AutocompleteRes(products=products, suggestions=autocomplete_suggestions)
