from typing import Dict, Set

from sqlalchemy import distinct
from sqlalchemy.orm import Session

from holmatro_customer_portal.database.enums import LanguageEnum
from holmatro_customer_portal.database.models import DBFilter, Product
from holmatro_customer_portal.schemas.opensearch_schemas import Filterable<PERSON><PERSON><PERSON><PERSON>e, ProductDocument, TranslatedName, \
    ProductNameSuggest
from holmatro_customer_portal.scripts.synonym_processor import SynonymProcessor
from holmatro_customer_portal.utils.database import construct_db_write_url, session_creator
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.open_search.opensearch_client import OpenSearchClient
from holmatro_customer_portal.utils.open_search.opensearch_mappings import PRODUCT_INDEX_MAPPINGS

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class ProductIndexer:
    """Handles indexing of products to OpenSearch."""

    def __init__(self, opensearch_client: OpenSearchClient) -> None:
        # Use environment variable if index_name is not provided
        self.opensearch_client = opensearch_client
        self.synonym_lookup: Dict[str, Set[str]] = {}

    def index_products(self, session: Session, force_full_reindex: bool = False) -> None:
        """Index products in OpenSearch: full reindex or delta updates."""

        # Load synonyms from CSV file if it exists
        synonym_processor = SynonymProcessor()
        self.synonym_lookup = synonym_processor.load_synonyms_from_csv()

        if force_full_reindex:
            self._perform_full_reindex(session)
        else:
            self._perform_delta_update(session)

        self._mark_dirty_products_clean(session)

    def _perform_full_reindex(self, session: Session) -> None:
        """Delete index, recreate it, and index all non-deleted products."""
        _logger.info(f"Performing full reindex")

        products = session.query(Product).filter(Product.deleted_at.is_(None)).all()
        _logger.info(f"Found {len(products)} products for full reindex")

        if products:
            self.opensearch_client.delete_index()
            self.opensearch_client.create_index(PRODUCT_INDEX_MAPPINGS)

            documents = [self._map_product_to_document(product, session) for product in products]
            success, failed = self.opensearch_client.bulk_index(documents)
            _logger.info(f"Successfully indexed {success} products")

    def _perform_delta_update(self, session: Session) -> None:
        """Update only dirty products (upsert active, delete soft-deleted)."""
        _logger.info("Performing delta update")

        # Ensure index exists
        self.opensearch_client.create_index(PRODUCT_INDEX_MAPPINGS)

        # Get dirty products
        dirty_active = session.query(Product).filter(Product.deleted_at.is_(None), Product.dirty == True).all()

        dirty_deleted = session.query(Product).filter(Product.deleted_at.is_not(None), Product.dirty == True).all()

        _logger.info(f"Found {len(dirty_active)} dirty active and {len(dirty_deleted)} dirty deleted products")

        # Prepare delta update data
        upsert_documents = [self._map_product_to_document(p, session) for p in dirty_active]
        delete_ids = [str(p.id) for p in dirty_deleted]

        if upsert_documents or delete_ids:
            results = self.opensearch_client.delta_update(upsert_documents, delete_ids)
            _logger.info(
                f"Delta update completed - Updated: {results['upserted'][0]}, Deleted: {results['deleted'][0]}"
            )
        else:
            _logger.info("No dirty products found to update")

    @staticmethod
    def _mark_dirty_products_clean(session: Session) -> None:
        """Mark all dirty products as clean after successful indexing."""
        dirty_count = session.query(Product).filter(Product.dirty == True).update({Product.dirty: False})
        session.commit()
        _logger.info(f"Marked {dirty_count} products as clean")

    @staticmethod
    def _get_attribute_ids(session: Session) -> list[int]:
        """Retrieve all unique attribute_ids from the Filter table."""
        return [attr_id for attr_id, in session.query(distinct(DBFilter.attribute_id)).all()]

    def _map_product_to_document(self, product: Product, session: Session) -> ProductDocument:
        """Convert a Product model to a ProductDocument for indexing."""
        product_names = [
            TranslatedName(language_code=pn.language.language_code, value=pn.value) for pn in product.product_names
        ]

        master_product_names = [
            TranslatedName(language_code=mpn.language.language_code, value=mpn.value)
            for mpn in product.master_product_names
        ]

        category_names = [
            TranslatedName(language_code=ct.language.language_code, value=ct.name or "No translation")
            for category_association in product.categories
            for ct in category_association.category.translations
        ]

        category_ids = [category_association.category.category_id for category_association in product.categories]

        # Prepare completion suggester data
        # product_name_suggestions = [pn.value for pn in product.product_names]
        # product_name_suggestions += [mpn.value for mpn in product.master_product_names]

        # Article number suggestions
        article_suggestions = [product.article_number]
        # Remove duplicates and empty strings
        product_name_suggestions: list[ProductNameSuggest] = []
        for pn in product_names:
            product_name_suggestions.append(ProductNameSuggest(input=pn.value, contexts={"language_context": pn.language_code}))
        article_suggestions = list(set(filter(None, article_suggestions)))

        # Retrieve all filterable attributes.
        filter_attribute_ids = self._get_attribute_ids(session)

        filterable_attributes = []
        matching_attributes = [attr for attr in product.attributes if attr.attribute_id in filter_attribute_ids]
        for attribute in matching_attributes:
            attribute_name = next(
                (t.name for t in attribute.translations if t.language.language_code == LanguageEnum.ENGLISH.value),
            )

            value_string = None
            value_numeric = None

            try:
                # Attempt to convert to float for numeric values
                value_numeric = float(attribute.content)
            except (ValueError, TypeError):
                # If conversion fails, treat as string
                value_string = attribute.content

            filterable_attributes.append(
                FilterableAttribute(
                    attribute_id=attribute.attribute_id,
                    attribute_name=str(attribute_name),
                    value_string=value_string,
                    value_numeric=value_numeric,
                )
            )

        synonyms_text = None
        if product.article_number and product.article_number in self.synonym_lookup:
            synonyms_set = self.synonym_lookup[product.article_number]
            synonyms_text = " ".join(synonyms_set) if synonyms_set else None

        return ProductDocument(
            id=product.id,
            product_id=product.product_id,
            umid=product.umid,
            article_number=product.article_number,
            synonyms=synonyms_text,
            product_names=product_names,
            master_product_names=master_product_names,
            category_names=category_names,
            category_ids=category_ids,
            filterable_attributes=filterable_attributes,
            last_modified=product.last_modified,
            product_name_suggest=product_name_suggestions,
            article_number_suggest=article_suggestions,
        )


if __name__ == "__main__":
    opensearch_client = OpenSearchClient(is_fuzzy_search_enabled=False, index_name=Env.OPENSEARCH_INDEX_NAME.get())
    ProductIndexer(opensearch_client=opensearch_client).index_products(
        session_creator(construct_db_write_url())(), force_full_reindex=True
    )
