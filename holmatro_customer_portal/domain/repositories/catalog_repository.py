from abc import abstractmethod
from typing import Protocol

from holmatro_customer_portal.database.enums import LanguageEnum
from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.schemas.response_schema import ParamsReq, SearchFiltersReq, SearchParamsReq
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import (
    Autocomplete,
    Category,
    FacetsAttributes,
    FilterAttribute,
    Navigation,
    ProductsResponse,
)


class CatalogRepository(Protocol):

    @abstractmethod
    def get_products(self, params: ParamsReq, filters: SearchFiltersReq | None = None) -> ProductsResponse:
        raise NotImplementedError

    @abstractmethod
    def get_category_tree(self, category_id: int) -> Category:
        raise NotImplementedError

    @abstractmethod
    def get_navigation(self, user: User, params: ParamsReq, filters: SearchFiltersReq | None = None) -> Navigation:
        raise NotImplementedError

    @abstractmethod
    def get_autocomplete(self, params: SearchParamsReq, language_code: str) -> Autocomplete:
        raise NotImplementedError

    @abstractmethod
    def get_category_path(
        self, language: str, category: SyncforceCategory, parent_category: SyncforceCategory
    ) -> str | None:
        raise NotImplementedError

    @abstractmethod
    def get_facet_attributes(
        self, urlkey: str, category: SyncforceCategory, parent_category: SyncforceCategory
    ) -> list[FacetsAttributes | FilterAttribute] | None:
        raise NotImplementedError
